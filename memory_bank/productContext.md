# Product Context: Better Grain Website

## 1. Core Product Offering

The "product" is the Better Grain company website. Its primary function is to serve as a comprehensive digital platform for the brand, providing information, showcasing products, and potentially facilitating customer interaction and sales.

## 2. Key User-Facing Components & Pages

Based on the project's HTML files, the website offers the following key sections and functionalities:

*   **Homepage (`index.html`):**
    *   Likely serves as the main landing page.
    *   Introduces the Better Grain brand, its mission ("Better Grain, Better Health, Better Planet"), and core product (Einkorn).
    *   May feature highlights, new products, or call-to-actions.

*   **About Us (`about.html`):**
    *   Provides detailed information about the Better Grain company.
    *   Could include company history, values, team information, and commitment to quality or sustainability.

*   **Our Farmers (`farmers.html`):**
    *   Highlights the agricultural partners or sourcing practices of Better Grain.
    *   Emphasizes transparency, connection to the land, and potentially sustainable farming methods.

*   **Products Section:**
    *   **General Products Page (`products.html`):** An overview or catalog of all products offered by Better Grain.
    *   **Detailed Product Pages:**
        *   `einkorn-berries.html`: Specific information about Einkorn berries (description, benefits, usage, pricing, images).
        *   `einkorn-flour.html`: Specific information about Einkorn flour.
        *   (Other product pages may exist or be planned).

*   **Contact Page (`contact.html`):**
    *   Provides means for users to get in touch with Better Grain (e.g., contact form, email address, phone number, physical address).

*   **E-commerce Features (Implied):**
    *   **Shopping Cart (`cart.html`):** Allows users to collect products they intend to purchase. Managed by `js/cart.js`.
    *   **Checkout (`checkout.html`):** Facilitates the purchase process, including shipping and payment information.

## 3. Administrative Features (Implied)

*   **Admin Panel (`admin.html`):**
    *   A backend interface likely for site administrators.
    *   Potential functionalities: product management, order management, content updates, user management.
    *   Supported by `test_admin.py`, suggesting specific logic and testing for these features.

## 4. Brand & Content Focus

*   **Einkorn Grain:** Central to the brand and product line. The website aims to educate users about its benefits and unique qualities.
*   **Health & Wellness:** A key theme, positioning Better Grain products as beneficial for a healthy lifestyle.
*   **Sustainability & Planet:** The slogan "Better Grain, Better Health, Better Planet" indicates a commitment to environmentally friendly practices, which is likely reflected in the website content.
*   **Quality & Trust:** The website aims to build trust by being transparent (e.g., `farmers.html`) and providing detailed product information.

## 5. Visual Identity

*   **Logo:** The company logo is present in the `Logo/` directory (e.g., `better grain.svg`).
*   **Imagery:** Product images, farm-related visuals, and lifestyle imagery are likely used throughout the site (stored in `Images/`).
*   **Design Aesthetics:** (To be further detailed by examining CSS and page layouts) - aims for a feel that aligns with natural, healthy, and premium quality products. 