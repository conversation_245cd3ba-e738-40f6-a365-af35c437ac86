# System Patterns: Better Grain Website

## 1. Overall Architecture

*   **Static Frontend Focus:** The primary user-facing website appears to be built as a collection of static HTML, CSS, and JavaScript files.
*   **Decoupled Admin/Backend (Probable):** The presence of `admin.html` and Python test files (`test_admin.py`, `backend_test.py`) suggests a separate or decoupled backend system for administrative tasks, which might be Python-based.
*   **Client-Side Interactivity:** JavaScript (`main.js`, `cart.js`) is used to enhance user experience on the client-side, particularly for features like the shopping cart.

## 2. Frontend Structure & Patterns

*   **Page-per-Feature:** Each major section/feature of the website has its own HTML file (e.g., `index.html`, `about.html`, `products.html`, `cart.html`).
*   **Centralized CSS:** A main stylesheet (`css/style.css`) likely holds common styles, potentially augmented by utility classes from Tailwind CSS and some inline styles.
*   **Modular JavaScript (Likely):**
    *   `main.js`: For global scripts, navigation, and common UI elements.
    *   `cart.js`: Specific functionality for the cart page, indicating a modular approach to JS for different features.
*   **Standard HTML5 Structure:** Pages are expected to follow semantic HTML5 conventions (header, nav, main, article, footer, etc.).
*   **Asset Organization:** Separate directories for `css/`, `js/`, `Images/`, and `Logo/` indicate a structured approach to asset management.
*   **Navigation:** A consistent navigation bar/menu is likely implemented across pages to ensure a cohesive user experience. (To be verified by inspecting HTML structure).
*   **Footer:** A common footer section is expected across pages, containing links, copyright information, etc. (To be verified).

## 3. CSS Styling Approach

*   **Hybrid Styling:** Combination of a primary custom stylesheet (`style.css`) and Tailwind CSS (CDN version) for utility-first styling.
*   **Google Fonts & Remix Icons:** Consistent use of specific font families and an icon set for visual uniformity.
*   **Responsive Design (Assumed):** Modern web practices and the use of a framework like Tailwind suggest that responsive design principles are likely applied to ensure usability across different screen sizes.

## 4. Backend/Administrative Patterns (Inferred)

*   **Dedicated Admin Interface:** `admin.html` suggests a separate web interface for administrative tasks.
*   **Python for Backend Logic:** Python test files imply Python is the language of choice for any server-side logic supporting the admin panel or other backend processes.
*   **API-Driven (Potentially):** If the admin panel interacts with a database or manages dynamic content, it might do so via API endpoints served by the Python backend.
*   **Testing:** Dedicated test files (`test_admin.py`, `backend_test.py`) indicate a pattern of writing tests for backend components.

## 5. Data Management

*   **Frontend (User Data):** For features like the shopping cart, data is likely managed client-side using JavaScript (e.g., local storage or session storage) before being sent to a backend for processing during checkout.
*   **Backend (Product/Order Data):** A database (not specified) is presumably used on the backend to store product information, customer orders, etc., managed via the admin panel.

## 6. Common UI Components (Hypothesized - To Be Verified)

*   Header/Navigation Bar
*   Footer
*   Product Cards/Listings
*   Image Sliders/Carousels (Potentially on homepage or product pages)
*   Forms (Contact, Checkout, Admin Login)
*   Buttons and Call-to-Action elements

## 7. Naming Conventions

*   **HTML files:** Lowercase, hyphen-separated (e.g., `einkorn-flour.html`).
*   **CSS/JS files:** Lowercase (e.g., `style.css`, `main.js`, `cart.js`).
*   **Python test files:** Lowercase, underscore-separated (e.g., `test_admin.py`).

*(This section will be updated as more detailed code reviews are performed.)* 