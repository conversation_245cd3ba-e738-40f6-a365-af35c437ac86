# Task List

## Priority Tasks

1.  **Implement Navigation Links:** Ensure consistent navigation (header/footer links) exists across all HTML pages (`index.html`, `products.html`, `about.html`, `farmers.html`, `contact.html`) allowing users to move between them. **(DONE)**
2.  **Consolidate Styling & Implement Mobile Nav:** 
    - Moved common CSS rules from inline `<style>` to `css/style.css`. **(DONE)**
    - Implemented basic mobile navigation drawer HTML & toggle JS (`js/main.js`). **(DONE)**
    - **Note:** Further refinement of mobile nav styling and potentially moving more specific component styles (e.g., product filters, timeline) to shared CSS could be done.
3.  **Refactor Large Files:** Break down `index.html`, `products.html`, and `about.html` into smaller, more manageable sections or components (conceptual if not using a framework).
4.  **Add Planned JavaScript:** Implement required JavaScript functionality (beyond basic nav toggle).

## New: AI Search Optimization (AISO) Tasks

### AISO Phase 1: Foundational Improvements

1.  **Content Enhancement for AI & User Intent (E-E-A-T):**
    *   **Task:** Review and expand content on core pages (`index.html`, `about.html`, `farmers.html`, `products.html`, `einkorn-berries.html`, `einkorn-flour.html`, `contact.html`) to be more comprehensive, authoritative, and address user intent for AI search.
    *   **Details:** Incorporate details from AISO Plan 1.1 (unique selling propositions, company story, farmer details, exhaustive product info, address common questions). As per [How to Optimize Your Website and Content to Rank in AI Search Results (xponent21.com)](https://xponent21.com/insights/optimize-content-rank-in-ai-search-results/), in-depth content is favored.
    *   **Affected Files:** `index.html`, `about.html`, `farmers.html`, `products.html`, `einkorn-berries.html`, `einkorn-flour.html`, `contact.html`, `article-history-of-einkorn.html`.
    *   **Status:** LARGELY DONE (core pages addressed; new content like articles will also apply this)

2.  **Technical SEO Enhancements:**
    *   **Task:** Implement technical SEO improvements.
    *   **Details:**
        *   Verify/enhance mobile-friendliness.
        *   Optimize page speed (images, caching, minification).
        *   Ensure proper `robots.txt` and generate/submit XML sitemap. **(robots.txt, sitemap.xml created)**
        *   Verify HTTPS across site. **(Assumed, as it's standard)**
        *   Strengthen internal linking strategy with descriptive anchor text. **(Partially addressed during content enhancements)**
    *   **Affected Files:** All HTML files, `css/*`, `js/*`, `robots.txt`, `sitemap.xml`.
    *   **Status:** PARTIALLY DONE (robots, sitemap, some internal links done; mobile & speed need full review)

### AISO Phase 2: Structured Data (Schema.org) Implementation

3.  **Implement `Organization` Schema:**
    *   **Task:** Add `Organization` schema markup (JSON-LD) to `index.html`.
    *   **Details:** Include company name, logo, contact, URL, slogan.
    *   **Affected Files:** `index.html`.
    *   **Status:** DONE

4.  **Implement `Product` Schema:**
    *   **Task:** Add `Product` schema markup (JSON-LD) to all individual product pages (`einkorn-berries.html`, `einkorn-flour.html`).
    *   **Details:** Include name, description, image, brand, SKU, offers (price, currency, availability).
    *   **Affected Files:** `einkorn-berries.html`, `einkorn-flour.html`.
    *   **Status:** DONE

5.  **Implement `Article`/`WebPage` Schema:**
    *   **Task:** Add appropriate schema (e.g., `Article`, `AboutPage`, `ContactPage`, `CollectionPage`) to informational and listing pages.
    *   **Details:** `AboutPage` for `about.html`, `ContactPage` for `contact.html`, `CollectionPage` for `farmers.html` & `products.html`. `Article` for `article-history-of-einkorn.html`.
    *   **Affected Files:** `about.html`, `contact.html`, `farmers.html`, `products.html`, `article-history-of-einkorn.html`.
    *   **Status:** DONE

6.  **Implement `BreadcrumbList` Schema:**
    *   **Task:** Add `BreadcrumbList` schema markup (JSON-LD) to all pages except the homepage.
    *   **Affected Files:** All HTML files except `index.html`.
    *   **Status:** TO DO (Requires consistent breadcrumb UI element first)

7.  **Implement `FAQPage` Schema (Conditional):**
    *   **Task:** If FAQ sections exist or are added to pages, implement `FAQPage` schema.
    *   **Affected Files:** `faq.html`.
    *   **Status:** DONE (New `faq.html` created with schema)

8.  **Implement `Recipe` Schema (Conditional):**
    *   **Task:** For the planned `recipes.html` (or any recipe content), implement `Recipe` schema.
    *   **Affected Files:** `recipes.html` (future).
    *   **Status:** To Do (Pending content creation)

9.  **Validate All Structured Data:**
    *   **Task:** Test all implemented schema using Google's Rich Results Test and Schema Markup Validator.
    *   **Details:** Iterate and fix errors until valid.
    *   **Affected Files:** All files with new schema.
    *   **Status:** TO DO (Crucial next step after schema implementation)

### AISO Phase 3: Content Structuring for Snippets & AI Overviews

10. **Enhance Content Scannability:**
    *   **Task:** Review and restructure content on all key pages for better scannability by AI and users.
    *   **Details:** Ensure logical heading structure (H1-H6), use lists, concise paragraphs, bold key terms.
    *   **Affected Files:** All major HTML content pages.
    *   **Status:** LARGELY DONE (Applied during content enhancements)

11. **Target Featured Snippets:**
    *   **Task:** Identify and integrate Q&A formats to target featured snippets and AI Overviews.
    *   **Details:** Answer common user questions directly and concisely within content. (FAQ page is a major step).
    *   **Affected Files:** All major HTML content pages, especially product and informational pages, `faq.html`.
    *   **Status:** PARTIALLY DONE (FAQ page created; further integration into other pages can be done)

### AISO Phase 4: E-E-A-T Reinforcement (Content Tasks)

12. **Strengthen E-E-A-T Signals:**
    *   **Task:** Review and enhance content to better showcase Experience, Expertise, Authoritativeness, and Trustworthiness.
    *   **Details:** Add author bios (if applicable), cite sources, prominently display reviews/testimonials, ensure `about.html` and `farmers.html` are comprehensive.
    *   **Affected Files:** `about.html`, `farmers.html`, product pages, `article-history-of-einkorn.html`.
    *   **Status:** LARGELY DONE (Applied during content enhancements and article creation)

### AISO Phase 5: Monitoring (Post-Implementation)

13. **Set up/Review Search Console & Analytics:**
    *   **Task:** Ensure Google Search Console and web analytics are properly configured for monitoring AISO impact.
    *   **Status:** To Do (Ongoing after implementation)

## Backlog/Ideas

- Create content pages: `regenerative.html`, `recipes.html` (Now has AISO considerations like `Recipe` schema)
- Create more blog articles based on AISO Task 5 topics.
- Implement `BreadcrumbList` schema after UI is in place.
- Full review for Mobile-friendliness and Page Speed optimization.
- Comprehensive Internal Linking review and enhancement.
- Evaluate and potentially implement a web framework (e.g., Astro, Next.js) for better structure and maintainability.
- Review and enhance accessibility (ARIA attributes, semantic HTML), especially for the dropdown and mobile navigation.
- Optimize images and other assets. 
- Refine mobile navigation styling. 