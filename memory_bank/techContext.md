# Technical Context

## 1. Core Frontend Technologies

*   **HTML:** Standard HTML5 used for the structure of all web pages.
*   **CSS:**
    *   **Custom CSS:** Main styles are located in `css/style.css`.
    *   **Tailwind CSS (v3 via CDN):** Utilized as the primary CSS framework for utility classes, styling, and layout. (As per original `techContext.md`)
    *   **Inline Styles:** Some custom styles may be defined within `<style>` tags in HTML files (e.g., `index.html`). (As per original `techContext.md`)
    *   **Google Fonts:** Specific typography is imported and used (e.g., `Pacifico`, `Playfair Display`, `Open Sans`). (As per original `techContext.md`)
    *   **Remix Icons:** Icon library used for iconography. (As per original `techContext.md`)
*   **JavaScript:**
    *   **`js/main.js`:** Likely contains general site-wide scripts, interactivity, and DOM manipulation.
    *   **`js/cart.js`:** Dedicated script for shopping cart functionalities (e.g., adding items, updating quantities, calculating totals).

## 2. Development & Build Environment

*   **Static Site:** The project is primarily a collection of static HTML, CSS, and JavaScript files.
*   **No Complex Build Process:** No specific build tools (like Webpack, Parcel, Vite) or JavaScript frameworks (like React, Vue, Angular) are explicitly mentioned or required for the basic operation of the website.
*   **Local Development Server:**
    *   A simple Python HTTP server is recommended for local development and testing.
    *   Command: `python3 -m http.server <PORT> --bind 0.0.0.0`
    *   This allows easy viewing of pages and testing on multiple devices on the local network.

## 3. Backend & Testing (Inferred)

*   **Python:**
    *   Used for the local development HTTP server.
    *   Presence of `backend_test.py` and `test_admin.py` suggests Python (possibly with a framework like Flask or Django, though not specified) might be used for:
        *   Backend API endpoints.
        *   Admin panel functionalities.
        *   Server-side logic or data processing (details not yet clear).
*   **Testing:**
    *   `test_admin.py`: Unit tests or integration tests for admin functionalities.
    *   `backend_test.py`: Tests for other backend components.
    *   `test_report.md`: Contains a report of test executions, likely from these test files.

## 4. Version Control

*   **Git:** The project is managed under Git version control (implied by the `.git` directory).

## 5. Assets

*   **Images:** Located in the `Images/` directory.
*   **Logos:** Located in the `Logo/` directory (e.g., `better grain.svg`).

## 6. Potential Future Enhancements/Considerations (from original techContext.md)

*   **Shared CSS/Componentization:** Consolidate styles, potentially extract Tailwind components/config, or move custom styles to a shared CSS file.
*   **Framework Adoption:** Consider Astro or Next.js if complexity grows or build processes become necessary.
*   **JavaScript Interactivity:** Further enhance planned JS features.
*   **Build Process:** Introduce a build tool if using a framework or advanced CSS/JS processing.

## 7. Infrastructure (Deployment)

*   **Unknown:** Deployment method and hosting environment are not yet defined in the provided documents. 