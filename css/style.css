/* Global Styles & Fonts */
body {
    font-family: 'Lato', sans-serif; /* VIS Body Font */
    background-color: #FAF8F5; /* VIS Primary Light (Default Page Background) */
    color: #4c312a; /* VIS Primary Dark (Main Text) */
    line-height: 1.7; /* VIS Line Height */
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Merriweather', serif; /* VIS Heading Font */
    letter-spacing: 0.5px;
    color: #4c312a; /* VIS Primary Dark (Headings) */
}
p {
    margin-bottom: 20px; /* VIS Paragraph bottom margin */
}

/* Common Textures & Overlays */
.grain-texture {
    position: relative;
    z-index: 1; /* Ensure content is above the texture */
}

.grain-texture::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://readdy.ai/api/search-image?query=A%20very%20subtle%2C%20almost%20imperceptible%20linen%20or%20grain%20texture%20pattern.%20The%20texture%20should%20be%20extremely%20light%20and%20delicate%2C%20appearing%20as%20tiny%20specks%20or%20fibers%20on%20a%20neutral%20beige%20background.%20This%20should%20be%20a%20seamless%20pattern%20that%20could%20be%20used%20as%20a%20subtle%20background%20texture%20without%20being%20distracting.&width=200&height=200&seq=2&orientation=squarish');
    background-size: 200px;
    opacity: 0.05;
    pointer-events: none;
    z-index: -1; /* Place the texture behind the content */
}

/* Navigation Link Styles */
.nav-link {
    position: relative;
    color: #4c312a; /* VIS Text Color */
    /* Tailwind classes can override hover text color */
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -4px; /* Adjust as needed */
    left: 0;
    background-color: #b4722c; /* VIS Golden Grain underline */
    transition: width 0.3s ease;
}

/* Apply underline on hover or when active */
.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}
.nav-link:hover, .nav-link.active {
    color: #b4722c; /* VIS Hover/Active State Text color */
}

/* Main Content Container Helper */
.main-content-container {
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 30px;
    padding-right: 30px;
}

/* Section Spacing Helper */
.section-spacing {
    padding-top: 80px;
    padding-bottom: 80px;
}

/* Common Interactive Element Styles */
.card-vis { /* VIS Card Style */
    background-color: #FFFFFF; /* White or #FAF8F5 if page BG is different */
    border-radius: 12px; /* VIS Card border-radius */
    box-shadow: 0px 6px 20px rgba(76, 49, 42, 0.08); /* VIS Card box-shadow */
    padding: 30px; /* VIS Card internal padding */
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-vis:hover {
    transform: translateY(-5px);
    box-shadow: 0px 10px 25px rgba(76, 49, 42, 0.12); /* Slightly enhanced shadow on hover */
}

/* VIS Button Styles */
.btn-base-vis {
    border-radius: 8px; /* VIS Rounded corners */
    font-weight: 600; /* VIS Semibold */
    font-size: 18px; /* VIS Font size */
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, transform 0.2s ease;
    display: inline-block;
    text-align: center;
    line-height: normal; /* Ensure proper button text alignment */
}

.btn-primary-vis {
    background-color: #b4722c; /* VIS Golden Grain */
    color: #FAF8F5; /* VIS Light Cream */
    padding: 15px 30px; /* VIS Padding */
}
.btn-primary-vis:hover {
    background-color: #a56A20; /* Darker Golden Grain */
}

.btn-secondary-vis {
    background-color: transparent;
    border: 2px solid #9e5f30; /* VIS Terracotta Earth */
    color: #9e5f30; /* VIS Terracotta Earth */
    padding: 13px 28px; /* VIS Padding (slightly less due to border) */
}
.btn-secondary-vis:hover {
    background-color: #F9F5F0; /* Very light tint of Terracotta Earth */
    color: #4c312a; /* Rich Soil */
}

/* Form Input Focus Styles */
input:focus,
textarea:focus,
select:focus { /* Added select for products page */
    border-color: #D2B48C !important; /* Tan border */
    border-color: #b4722c !important; /* VIS Golden Grain border */
    outline: 2px solid transparent; /* Remove default outline */
    box-shadow: 0 0 0 2px rgba(180, 114, 44, 0.4) !important; /* VIS Golden Grain ring */
}

/* Image Styles */
img.rounded-vis {
    border-radius: 12px; /* VIS Image border-radius */
}
img.shadow-vis {
    box-shadow: 0px 8px 25px rgba(76, 49, 42, 0.1); /* VIS Image shadow */
}

/* Drop Cap */
.drop-cap::first-letter {
    font-family: 'Merriweather', serif; /* VIS Heading Font */
    font-size: 3em;
    color: #b4722c; /* VIS Golden Grain */
    float: left;
    margin-right: 0.1em;
    line-height: 0.8;
    padding-top: 0.2em; /* Adjust for alignment */
}

/* Constrained Text Block */
.text-block-constrained {
    max-width: 70ch;
}

/* Basic Mobile Nav Styles (Placeholder - will be refined) */
#mobile-menu {
    transition: transform 0.3s ease-in-out;
    transform: translateX(100%); /* Start off-screen */
}

#mobile-menu.is-open {
    transform: translateX(0); /* Slide in */
}

/* Style for the body when mobile menu is open */
body.mobile-menu-open {
    overflow: hidden; /* Prevent scrolling */
}

/* Custom Bullets for Lists */
ul.custom-bullets {
    list-style-type: none;
    padding-left: 0;
}
ul.custom-bullets li {
    position: relative;
    padding-left: 25px; /* Space for bullet */
    margin-bottom: 0.75rem; /* Small spacing */
    color: #4c312a; /* Rich Soil */
}
ul.custom-bullets li::before {
    content: ''; /* Or use an icon font/SVG */
    position: absolute;
    left: 0;
    top: 0.5em; /* Adjust for vertical alignment with line-height 1.7 */
    width: 8px; /* Small dot */
    height: 8px;
    background-color: #b4722c; /* Golden Grain */
    border-radius: 50%;
}
ul.custom-bullets li strong {
    font-weight: bold; /* As per VIS */
    color: #4c312a; /* Rich Soil */
}