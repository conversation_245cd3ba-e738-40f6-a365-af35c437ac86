/* Global Styles & Fonts */
body {
    font-family: 'Open Sans', sans-serif;
    background-color: #F8F4EC; /* Light Beige Background */
    color: #5C4033; /* Dark Brown text */
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    letter-spacing: 0.5px;
    color: #5C4033; /* Dark Brown heading */
}

/* Common Textures & Overlays */
.grain-texture {
    position: relative;
    z-index: 1; /* Ensure content is above the texture */
}

.grain-texture::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://readdy.ai/api/search-image?query=A%20very%20subtle%2C%20almost%20imperceptible%20linen%20or%20grain%20texture%20pattern.%20The%20texture%20should%20be%20extremely%20light%20and%20delicate%2C%20appearing%20as%20tiny%20specks%20or%20fibers%20on%20a%20neutral%20beige%20background.%20This%20should%20be%20a%20seamless%20pattern%20that%20could%20be%20used%20as%20a%20subtle%20background%20texture%20without%20being%20distracting.&width=200&height=200&seq=2&orientation=squarish');
    background-size: 200px;
    opacity: 0.05;
    pointer-events: none;
    z-index: -1; /* Place the texture behind the content */
}

/* Navigation Link Styles */
.nav-link {
    position: relative;
    /* Tailwind classes handle text color, hover - keep base structure */
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -4px;
    left: 0;
    background-color: #D2B48C; /* Tan underline for active/hover */
    transition: width 0.3s ease;
}

/* Apply underline on hover or when active */
.nav-link:hover::after, 
.nav-link.active::after { 
    width: 100%;
}

/* Common Interactive Element Styles */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(92, 64, 51, 0.1); /* Dark brown shadow */
}

.btn-primary {
    /* Base styles are handled by Tailwind, this adds transition */
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.btn-primary:hover {
    /* Tailwind handles bg/text color, this adds hover effect */
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(210, 180, 140, 0.3); /* Tan shadow */
}

/* Form Input Focus Styles */
input:focus, 
textarea:focus,
select:focus { /* Added select for products page */
    border-color: #D2B48C !important; /* Tan border */
    outline: 2px solid transparent; /* Remove default outline */
    box-shadow: 0 0 0 2px rgba(210, 180, 140, 0.4); /* Tan ring */
}

/* Base Icon fix */
/* :where([class^="ri-"])::before { content: "\f3c2"; } /* This seems like a fallback/error, let's remove it unless icons break */

/* Basic Mobile Nav Styles (Placeholder - will be refined) */
#mobile-menu {
    transition: transform 0.3s ease-in-out;
    transform: translateX(100%); /* Start off-screen */
}

#mobile-menu.is-open {
    transform: translateX(0); /* Slide in */
}

/* Style for the body when mobile menu is open */
body.mobile-menu-open {
    overflow: hidden; /* Prevent scrolling */
} 