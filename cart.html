<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Better Grain - Shopping Cart</title>
<script src="https://cdn.tailwindcss.com/3.4.16"></script>
<script>
    tailwind.config = {
        theme: {
            extend: {
                fontFamily: {
                    'merriweather': ['Merriweather', 'serif'],
                    'lato': ['Lato', 'sans-serif']
                },
                colors: {
                    primary: '#b4722c', // VIS Golden Grain (CTAs, Links, Key Icons)
                    secondary: '#4c312a', // VIS Rich Soil (Headings, Main Text)
                    'page-bg': '#FAF8F5', // VIS Light Warm Cream (Default Page Background)
                    'terracotta-earth': '#9e5f30', // VIS Terracotta Earth (Secondary Buttons, Subtle Accents)
                    'golden-grain': '#b4722c', // VIS Golden Grain
                    'light-warm-cream': '#FAF8F5', // VIS Light Warm Cream (Text on Dark Backgrounds)
                    'medium-brown': '#8B7355', // Legacy support
                },
                borderRadius: {
                    'none': '0px',
                    'sm': '4px',
                    DEFAULT: '8px',
                    'md': '12px',
                    'lg': '16px',
                    'xl': '20px',
                    '2xl': '24px',
                    '3xl': '32px',
                    'full': '9999px',
                    'button': '8px'
                }
            }
        }
    }
</script>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Open+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Lato:wght@400;600&family=Merriweather:wght@400;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
<link rel="stylesheet" href="css/style.css">
<style>
    .quantity-input {
        width: 50px;
        text-align: center;
    }

    .empty-cart-illustration {
        max-width: 250px;
        margin: 0 auto;
    }

    .btn-quantity {
        transition: all 0.2s ease;
    }

    .btn-quantity:hover {
        background-color: rgba(92, 64, 51, 0.1);
    }

    .cart-table th {
        font-weight: 600;
        text-align: left;
        padding: 12px 16px;
        border-bottom: 1px solid rgba(92, 64, 51, 0.1);
    }

    .cart-table td {
        padding: 16px;
        border-bottom: 1px solid rgba(92, 64, 51, 0.05);
    }

    .cart-item {
        transition: background-color 0.3s ease;
    }

    .cart-item:hover {
        background-color: rgba(92, 64, 51, 0.02);
    }

    .cart-summary-card {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(92, 64, 51, 0.08);
    }
</style>
</head>
<body class="min-h-screen bg-page-bg">
<!-- Header & Navigation -->
<header class="fixed w-full bg-white bg-opacity-95 shadow-sm z-50">
<div class="container mx-auto px-6 py-4 flex justify-between items-center">
<a href="index.html" class="flex items-center">
<img src="Logo/better grain.svg" alt="Better Grain Logo" class="h-24">
</a>
<nav class="hidden md:flex space-x-8">
<a href="index.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Home</a>
<a href="products.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Products</a>
<a href="about.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">About Us</a>
<a href="farmers.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Farmers</a>
<div class="relative group">
<button class="nav-link text-secondary hover:text-primary font-medium transition-colors flex items-center">
Resources <i class="ri-arrow-down-s-line ml-1"></i>
</button>
<div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 invisible group-hover:visible">
<a href="regenerative.html" class="block px-4 py-2 text-sm text-secondary hover:bg-primary hover:bg-opacity-10 hover:text-primary">Regenerative</a>
<a href="recipes.html" class="block px-4 py-2 text-sm text-secondary hover:bg-primary hover:bg-opacity-10 hover:text-primary">Recipes</a>
</div>
</div>
<a href="contact.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Contact</a>
</nav>

<!-- Cart Icon (desktop) -->
<div class="hidden md:flex items-center ml-6">
    <a href="cart.html" class="flex items-center text-secondary hover:text-primary relative p-2 group">
        <i class="ri-shopping-cart-line ri-lg"></i>
        <span class="cart-count absolute -top-1 -right-1 bg-primary text-secondary text-xs w-5 h-5 flex items-center justify-center rounded-full hidden">0</span>
    </a>
    <a href="#" class="flex items-center text-secondary hover:text-primary p-2 ml-2">
        <i class="ri-user-line ri-lg"></i>
    </a>
</div>

<!-- Mobile Menu Button -->
<div class="md:hidden flex items-center">
    <a href="cart.html" class="flex items-center text-secondary hover:text-primary relative p-2 mr-2">
        <i class="ri-shopping-cart-line ri-lg"></i>
        <span class="cart-count absolute -top-1 -right-1 bg-primary text-secondary text-xs w-5 h-5 flex items-center justify-center rounded-full hidden">0</span>
    </a>
    <a href="#" class="flex items-center text-secondary hover:text-primary p-2 mr-2">
        <i class="ri-user-line ri-lg"></i>
    </a>
    <button id="mobile-menu-button" class="text-secondary focus:outline-none ml-2">
        <i class="ri-menu-line ri-lg"></i>
    </button>
</div>
</div>
</header>

<!-- Mobile Menu Drawer -->
<div id="mobile-menu" class="fixed inset-y-0 right-0 w-64 bg-white shadow-lg p-6 z-50 md:hidden transform translate-x-full transition-transform duration-300 ease-in-out">
<div class="flex justify-between items-center mb-8">
<img src="Logo/better grain.svg" alt="Better Grain Logo" class="h-24">
<button id="mobile-menu-close-button" class="text-secondary focus:outline-none">
<i class="ri-close-line ri-lg"></i>
</button>
</div>
<nav class="flex flex-col space-y-4">
<a href="index.html" class="text-secondary hover:text-primary font-medium">Home</a>
<a href="products.html" class="text-secondary hover:text-primary font-medium">Products</a>
<a href="about.html" class="text-secondary hover:text-primary font-medium">About Us</a>
<a href="farmers.html" class="text-secondary hover:text-primary font-medium">Farmers</a>
<div>
<span class="font-medium text-secondary">Resources</span>
<div class="flex flex-col space-y-2 pl-4 mt-2">
<a href="regenerative.html" class="text-sm text-secondary hover:text-primary">Regenerative</a>
<a href="recipes.html" class="text-sm text-secondary hover:text-primary">Recipes</a>
</div>
</div>
<a href="contact.html" class="text-secondary hover:text-primary font-medium">Contact</a>
<a href="cart.html" class="text-secondary hover:text-primary font-medium">Cart</a>
</nav>
</div>

<!-- Page Title -->
<section class="pt-32 pb-12 bg-white grain-texture">
<div class="container mx-auto px-6">
<div class="max-w-6xl mx-auto">
<a href="products.html" class="inline-flex items-center text-secondary hover:text-primary mb-4">
<i class="ri-arrow-left-line mr-2"></i>
<span>Continue Shopping</span>
</a>
<h1 class="text-4xl md:text-5xl font-bold text-secondary mb-4">Your Shopping Cart</h1>
<p class="text-lg text-secondary opacity-80">Review your items and proceed to checkout when you're ready.</p>
</div>
</div>
</section>

<!-- Cart Contents Section -->
<section class="py-12 bg-page-bg min-h-[60vh]">
<div class="container mx-auto px-6">
    <div class="max-w-6xl mx-auto">
        <!-- This will be replaced with actual cart contents from JavaScript -->
        <div id="cart-container">
            <!-- Empty cart message (default state) -->
            <div id="empty-cart" class="text-center py-12">
                <div class="empty-cart-illustration mb-6">
                    <i class="ri-shopping-cart-line text-primary text-9xl opacity-30"></i>
                </div>
                <h2 class="text-2xl font-bold text-secondary mb-4">Your cart is empty</h2>
                <p class="text-secondary mb-8 max-w-md mx-auto">Looks like you haven't added any products to your cart yet. Browse our products and find something you'll love!</p>
                <a href="products.html" class="bg-primary text-secondary px-6 py-3 rounded-button font-medium inline-flex items-center hover:brightness-110 transition-all">
                    <i class="ri-store-2-line mr-2"></i>
                    Browse Products
                </a>
            </div>

            <!-- Cart items will be displayed here -->
            <div id="cart-items" class="hidden">
                <div class="flex flex-col lg:flex-row gap-8">
                    <!-- Cart items table -->
                    <div class="w-full lg:w-2/3">
                        <div class="bg-white rounded-lg shadow-sm overflow-x-auto">
                            <table class="cart-table w-full min-w-full table-auto">
                                <thead>
                                    <tr>
                                        <th class="w-12"></th> <!-- Remove button column -->
                                        <th class="w-16">Product</th>
                                        <th>Description</th>
                                        <th class="w-32">Price</th>
                                        <th class="w-32">Quantity</th>
                                        <th class="w-32">Subtotal</th>
                                    </tr>
                                </thead>
                                <tbody id="cart-table-body">
                                    <!-- Cart items will be inserted here by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Order summary -->
                    <div class="w-full lg:w-1/3">
                        <div class="cart-summary-card p-6">
                            <h3 class="text-xl font-bold text-secondary mb-6">Order Summary</h3>

                            <div class="space-y-4 mb-6">
                                <div class="flex justify-between">
                                    <span class="text-secondary">Subtotal</span>
                                    <span id="cart-subtotal" class="text-secondary font-medium">$0.00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-secondary">Shipping</span>
                                    <span id="cart-shipping" class="text-secondary font-medium">$5.99</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-secondary">Tax (7%)</span>
                                    <span id="cart-tax" class="text-secondary font-medium">$0.00</span>
                                </div>
                                <div class="border-t border-gray-200 pt-4 mt-4">
                                    <div class="flex justify-between">
                                        <span class="text-secondary font-bold">Total</span>
                                        <span id="cart-total" class="text-secondary font-bold">$5.99</span>
                                    </div>
                                </div>
                            </div>

                            <a href="checkout.html" id="checkout-button" class="bg-primary text-secondary w-full py-3 rounded-button font-medium inline-flex items-center justify-center hover:brightness-110 transition-all mb-4">
                                <i class="ri-secure-payment-line mr-2"></i>
                                Proceed to Checkout
                            </a>

                            <button id="clear-cart-button" class="border border-secondary text-secondary w-full py-3 rounded-button font-medium inline-flex items-center justify-center hover:bg-secondary hover:text-white transition-all">
                                <i class="ri-delete-bin-line mr-2"></i>
                                Clear Cart
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</section>

<!-- Footer -->
<footer class="bg-[#F8F4EC] pt-16 pb-8">
<div class="container mx-auto px-6">
<div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
<div>
<a href="index.html" class="inline-block mb-4">
<img src="Logo/better grain.svg" alt="Better Grain Logo" class="h-24 mb-0">
</a>
<p class="text-secondary opacity-80 text-sm leading-relaxed">Cultivating ancient grains for a healthier future and a thriving planet.</p>
<div class="flex space-x-4 mt-6">
<a href="#" class="text-secondary hover:text-primary"><i class="ri-facebook-fill ri-lg"></i></a>
<a href="#" class="text-secondary hover:text-primary"><i class="ri-instagram-line ri-lg"></i></a>
<a href="#" class="text-secondary hover:text-primary"><i class="ri-twitter-x-line ri-lg"></i></a>
</div>
</div>
<div>
<h4 class="font-bold text-secondary mb-4">Quick Links</h4>
<ul class="space-y-2">
<li><a href="index.html" class="text-secondary hover:text-primary text-sm">Home</a></li>
<li><a href="products.html" class="text-secondary hover:text-primary text-sm">Products</a></li>
<li><a href="about.html" class="text-secondary hover:text-primary text-sm">About Us</a></li>
<li><a href="farmers.html" class="text-secondary hover:text-primary text-sm">Our Farmers</a></li>
<li><a href="contact.html" class="text-secondary hover:text-primary text-sm">Contact</a></li>
</ul>
</div>
<div>
<h4 class="font-bold text-secondary mb-4">Resources</h4>
<ul class="space-y-2">
<li><a href="regenerative.html" class="text-secondary hover:text-primary text-sm">Regenerative Agriculture</a></li>
<li><a href="recipes.html" class="text-secondary hover:text-primary text-sm">Recipes & Guides</a></li>
<li><a href="faq.html" class="text-secondary hover:text-primary text-sm">FAQ</a></li>
</ul>
</div>
<div>
<h4 class="font-bold text-secondary mb-4">Contact Us</h4>
<address class="text-secondary opacity-80 text-sm not-italic space-y-2">
<p>123 Grain Lane, <br>Harvestville, CA 90210</p>
<p>Email: <a href="mailto:<EMAIL>" class="hover:text-primary"><EMAIL></a></p>
<p>Phone: <a href="tel:+1234567890" class="hover:text-primary">(*************</a></p>
</address>
</div>
</div>
<div class="border-t border-secondary border-opacity-20 pt-8 text-center">
<p class="text-secondary opacity-70 text-sm">&copy; 2024 Better Grain. All Rights Reserved.</p>
</div>
</div>
</footer>

<!-- JavaScript -->
<script src="js/main.js"></script>
<script src="js/cart.js"></script>
<script>
// Cart page specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const emptyCartEl = document.getElementById('empty-cart');
    const cartItemsEl = document.getElementById('cart-items');
    const cartTableBody = document.getElementById('cart-table-body');
    const cartSubtotalEl = document.getElementById('cart-subtotal');
    const cartTaxEl = document.getElementById('cart-tax');
    const cartTotalEl = document.getElementById('cart-total');
    const clearCartBtn = document.getElementById('clear-cart-button');
    const checkoutBtn = document.getElementById('checkout-button');

    // Shipping cost (fixed)
    const shippingCost = 5.99;
    const taxRate = 0.07; // 7% tax rate

    // Render the cart
    function renderCart() {
        const cart = CartManager.getCart();

        // Show empty cart message or cart items based on cart content
        if (cart.length === 0) {
            emptyCartEl.classList.remove('hidden');
            cartItemsEl.classList.add('hidden');
            return;
        }

        emptyCartEl.classList.add('hidden');
        cartItemsEl.classList.remove('hidden');

        // Clear existing cart items
        cartTableBody.innerHTML = '';

        // Add each cart item to the table
        cart.forEach(item => {
            const itemTotal = item.price * item.quantity;
            const row = document.createElement('tr');
            row.className = 'cart-item';
            row.innerHTML = `
                <td class="text-center">
                    <button class="remove-item-btn text-secondary hover:text-red-500 transition-colors p-2" data-product-id="${item.id}">
                        <i class="ri-close-line"></i>
                    </button>
                </td>
                <td>
                    <div class="w-16 h-16 rounded overflow-hidden">
                        <img src="${item.image}" alt="${item.name}" class="w-full h-full object-cover">
                    </div>
                </td>
                <td>
                    <a href="#" class="font-medium text-secondary hover:text-primary">${item.name}</a>
                </td>
                <td>$${item.price.toFixed(2)}</td>
                <td>
                    <div class="flex items-center">
                        <button class="decrement-btn btn-quantity w-8 h-8 flex items-center justify-center rounded-full focus:outline-none" data-product-id="${item.id}">
                            <i class="ri-subtract-line"></i>
                        </button>
                        <input type="number" class="quantity-input mx-2 px-2 py-1 border border-gray-200 rounded" value="${item.quantity}" min="1" data-product-id="${item.id}">
                        <button class="increment-btn btn-quantity w-8 h-8 flex items-center justify-center rounded-full focus:outline-none" data-product-id="${item.id}">
                            <i class="ri-add-line"></i>
                        </button>
                    </div>
                </td>
                <td class="font-medium">$${itemTotal.toFixed(2)}</td>
            `;
            cartTableBody.appendChild(row);
        });

        // Calculate and update totals
        updateTotals();

        // Add event listeners for quantity changes and remove buttons
        addCartEventListeners();
    }

    // Update cart totals
    function updateTotals() {
        const subtotal = CartManager.calculateTotal();
        const tax = subtotal * taxRate;
        const total = subtotal + tax + shippingCost;

        cartSubtotalEl.textContent = `$${subtotal.toFixed(2)}`;
        cartTaxEl.textContent = `$${tax.toFixed(2)}`;
        cartTotalEl.textContent = `$${total.toFixed(2)}`;
    }

    // Add event listeners for cart interactions
    function addCartEventListeners() {
        // Remove item buttons
        document.querySelectorAll('.remove-item-btn').forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.dataset.productId;
                CartManager.removeItem(productId);
                renderCart();
            });
        });

        // Quantity input changes
        document.querySelectorAll('.quantity-input').forEach(input => {
            input.addEventListener('change', function() {
                const productId = this.dataset.productId;
                const newQuantity = parseInt(this.value, 10);

                if (newQuantity > 0) {
                    CartManager.updateQuantity(productId, newQuantity);
                    renderCart();
                } else {
                    // Reset to 1 if invalid value entered
                    this.value = 1;
                }
            });
        });

        // Increment buttons
        document.querySelectorAll('.increment-btn').forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.dataset.productId;
                const currentItem = CartManager.getCart().find(item => item.id === productId);

                if (currentItem) {
                    CartManager.updateQuantity(productId, currentItem.quantity + 1);
                    renderCart();
                }
            });
        });

        // Decrement buttons
        document.querySelectorAll('.decrement-btn').forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.dataset.productId;
                const currentItem = CartManager.getCart().find(item => item.id === productId);

                if (currentItem && currentItem.quantity > 1) {
                    CartManager.updateQuantity(productId, currentItem.quantity - 1);
                    renderCart();
                } else if (currentItem && currentItem.quantity === 1) {
                    // Ask for confirmation before removing last item
                    if (confirm('Remove this item from your cart?')) {
                        CartManager.removeItem(productId);
                        renderCart();
                    }
                }
            });
        });
    }

    // Clear cart button
    if (clearCartBtn) {
        clearCartBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to clear your cart?')) {
                CartManager.clearCart();
                renderCart();
            }
        });
    }

    // Initial cart render
    renderCart();

    // Update cart display when storage changes (in case another tab updates it)
    window.addEventListener('storage', function(e) {
        if (e.key === 'betterGrainCart') {
            renderCart();
        }
    });
});
</script>
</body>
</html>