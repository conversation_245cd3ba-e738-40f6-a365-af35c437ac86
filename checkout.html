<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Better Grain - Checkout</title>
<script src="https://cdn.tailwindcss.com/3.4.16"></script>
<script>
    tailwind.config = {
        theme: {
            extend: {
                fontFamily: {
                    'merriweather': ['Merriweather', 'serif'],
                    'lato': ['Lato', 'sans-serif']
                },
                colors: {
                    primary: '#b4722c', // VIS Golden Grain (CTAs, Links, Key Icons)
                    secondary: '#4c312a', // VIS Rich Soil (Headings, Main Text)
                    'page-bg': '#FAF8F5', // VIS Light Warm Cream (Default Page Background)
                    'terracotta-earth': '#9e5f30', // VIS Terracotta Earth (Secondary Buttons, Subtle Accents)
                    'golden-grain': '#b4722c', // VIS Golden Grain
                    'light-warm-cream': '#FAF8F5', // VIS Light Warm Cream (Text on Dark Backgrounds)
                    'medium-brown': '#8B7355', // Legacy support
                },
                borderRadius: {
                    'none': '0px',
                    'sm': '4px',
                    DEFAULT: '8px',
                    'md': '12px',
                    'lg': '16px',
                    'xl': '20px',
                    '2xl': '24px',
                    '3xl': '32px',
                    'full': '9999px',
                    'button': '8px'
                }
            }
        }
    }
</script>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Open+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Lato:wght@400;600&family=Merriweather:wght@400;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
<link rel="stylesheet" href="css/style.css">
<style>
    .cart-summary-card {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(92, 64, 51, 0.08);
    }




</style>
</head>
<body class="min-h-screen bg-page-bg">
<!-- Header & Navigation -->
<header class="fixed w-full bg-white bg-opacity-95 shadow-sm z-50">
<div class="container mx-auto px-6 py-4 flex justify-between items-center">
<a href="index.html" class="flex items-center">
<img src="Logo/better grain.svg" alt="Better Grain Logo" class="h-24">
</a>
<nav class="hidden md:flex space-x-8">
<a href="index.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Home</a>
<a href="products.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Products</a>
<a href="about.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">About Us</a>
<a href="farmers.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Farmers</a>
<div class="relative group">
<button class="nav-link text-secondary hover:text-primary font-medium transition-colors flex items-center">
Resources <i class="ri-arrow-down-s-line ml-1"></i>
</button>
<div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 invisible group-hover:visible">
<a href="regenerative.html" class="block px-4 py-2 text-sm text-secondary hover:bg-primary hover:bg-opacity-10 hover:text-primary">Regenerative</a>
<a href="recipes.html" class="block px-4 py-2 text-sm text-secondary hover:bg-primary hover:bg-opacity-10 hover:text-primary">Recipes</a>
</div>
</div>
<a href="contact.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Contact</a>
</nav>

<!-- Cart Icon (desktop) -->
<div class="hidden md:flex items-center ml-6">
    <a href="cart.html" class="flex items-center text-secondary hover:text-primary relative p-2 group">
        <i class="ri-shopping-cart-line ri-lg"></i>
        <span class="cart-count absolute -top-1 -right-1 bg-primary text-secondary text-xs w-5 h-5 flex items-center justify-center rounded-full hidden">0</span>
    </a>
    <a href="#" class="flex items-center text-secondary hover:text-primary p-2 ml-2">
        <i class="ri-user-line ri-lg"></i>
    </a>
</div>

<!-- Mobile Menu Button -->
<div class="md:hidden flex items-center">
    <a href="cart.html" class="flex items-center text-secondary hover:text-primary relative p-2 mr-2">
        <i class="ri-shopping-cart-line ri-lg"></i>
        <span class="cart-count absolute -top-1 -right-1 bg-primary text-secondary text-xs w-5 h-5 flex items-center justify-center rounded-full hidden">0</span>
    </a>
    <a href="#" class="flex items-center text-secondary hover:text-primary p-2 mr-2">
        <i class="ri-user-line ri-lg"></i>
    </a>
    <button id="mobile-menu-button" class="text-secondary focus:outline-none ml-2">
        <i class="ri-menu-line ri-lg"></i>
    </button>
</div>
</div>
</header>

<!-- Mobile Menu Drawer -->
<div id="mobile-menu" class="fixed inset-y-0 right-0 w-64 bg-white shadow-lg p-6 z-50 md:hidden transform translate-x-full transition-transform duration-300 ease-in-out">
<div class="flex justify-between items-center mb-8">
<img src="Logo/better grain.svg" alt="Better Grain Logo" class="h-24">
<button id="mobile-menu-close-button" class="text-secondary focus:outline-none">
<i class="ri-close-line ri-lg"></i>
</button>
</div>
<nav class="flex flex-col space-y-4">
<a href="index.html" class="text-secondary hover:text-primary font-medium">Home</a>
<a href="products.html" class="text-secondary hover:text-primary font-medium">Products</a>
<a href="about.html" class="text-secondary hover:text-primary font-medium">About Us</a>
<a href="farmers.html" class="text-secondary hover:text-primary font-medium">Farmers</a>
<div>
<span class="font-medium text-secondary">Resources</span>
<div class="flex flex-col space-y-2 pl-4 mt-2">
<a href="regenerative.html" class="text-sm text-secondary hover:text-primary">Regenerative</a>
<a href="recipes.html" class="text-sm text-secondary hover:text-primary">Recipes</a>
</div>
</div>
<a href="contact.html" class="text-secondary hover:text-primary font-medium">Contact</a>
<a href="cart.html" class="text-secondary hover:text-primary font-medium">Cart</a>
</nav>
</div>

<!-- Page Title -->
<section class="pt-32 pb-12 bg-white grain-texture">
<div class="container mx-auto px-6">
<div class="max-w-6xl mx-auto">
<a href="cart.html" class="inline-flex items-center text-secondary hover:text-primary mb-4">
<i class="ri-arrow-left-line mr-2"></i>
<span>Back to Cart</span>
</a>
<h1 class="text-4xl md:text-5xl font-bold text-secondary mb-4">Checkout</h1>
<p class="text-lg text-secondary opacity-80">Complete your order by filling out the information below.</p>
</div>
</div>
</section>

<!-- Checkout Form Section -->
<section class="py-12 bg-page-bg">
<div class="container mx-auto px-6">
    <div class="max-w-6xl mx-auto">
        <!-- No items error message -->
        <div id="empty-checkout-error" class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8 hidden">
            <div class="flex items-start">
                <div class="text-red-500 mr-4">
                    <i class="ri-error-warning-line text-2xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-red-700 mb-1">Your cart is empty</h3>
                    <p class="text-red-600">You need to add items to your cart before checking out.</p>
                    <a href="products.html" class="inline-flex items-center text-red-700 hover:text-red-800 font-medium mt-2">
                        <i class="ri-arrow-right-line mr-2"></i>
                        Browse Products
                    </a>
                </div>
            </div>
        </div>

        <div class="flex flex-col lg:flex-row gap-8" id="checkout-content">
            <!-- Checkout Form -->
            <div class="w-full lg:w-2/3">
                <form id="checkout-form" class="space-y-6">
                    <!-- Shipping Information -->
                    <div class="checkout-section bg-white rounded-2xl shadow-lg p-8 mb-8 border border-gray-100">
                        <h2 class="checkout-section-title text-2xl font-bold text-secondary mb-6 pb-2 border-b border-gray-100">Shipping Information</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="first-name" class="block text-secondary text-sm font-semibold mb-1">First Name</label>
                                <input type="text" id="first-name" class="w-full px-3 py-2 rounded-lg border border-gray-200 text-secondary text-base focus:ring-2 focus:ring-primary focus:border-primary bg-gray-50 transition-all" required>
                            </div>
                            <div>
                                <label for="last-name" class="block text-secondary text-sm font-semibold mb-1">Last Name</label>
                                <input type="text" id="last-name" class="w-full px-3 py-2 rounded-lg border border-gray-200 text-secondary text-base focus:ring-2 focus:ring-primary focus:border-primary bg-gray-50 transition-all" required>
                            </div>
                        </div>
                        <div class="mt-4">
                            <label for="email" class="block text-secondary text-sm font-semibold mb-1">Email Address</label>
                            <input type="email" id="email" class="w-full px-3 py-2 rounded-lg border border-gray-200 text-secondary text-base focus:ring-2 focus:ring-primary focus:border-primary bg-gray-50 transition-all" required>
                        </div>
                        <div class="mt-4">
                            <label for="phone" class="block text-secondary text-sm font-semibold mb-1">Phone Number</label>
                            <input type="tel" id="phone" class="w-full px-3 py-2 rounded-lg border border-gray-200 text-secondary text-base focus:ring-2 focus:ring-primary focus:border-primary bg-gray-50 transition-all" required>
                        </div>
                        <div class="mt-4">
                            <label for="address" class="block text-secondary text-sm font-semibold mb-1">Street Address</label>
                            <input type="text" id="address" class="w-full px-3 py-2 rounded-lg border border-gray-200 text-secondary text-base focus:ring-2 focus:ring-primary focus:border-primary bg-gray-50 transition-all" required>
                        </div>
                        <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-3">
                            <div>
                                <label for="city" class="block text-secondary text-sm font-semibold mb-1">City</label>
                                <input type="text" id="city" class="w-full px-3 py-2 rounded-lg border border-gray-200 text-secondary text-base focus:ring-2 focus:ring-primary focus:border-primary bg-gray-50 transition-all" required>
                            </div>
                            <div>
                                <label for="state" class="block text-secondary text-sm font-semibold mb-1">State</label>
                                <input type="text" id="state" class="w-full px-3 py-2 rounded-lg border border-gray-200 text-secondary text-base focus:ring-2 focus:ring-primary focus:border-primary bg-gray-50 transition-all" required>
                            </div>
                            <div>
                                <label for="zip" class="block text-secondary text-sm font-semibold mb-1">ZIP Code</label>
                                <input type="text" id="zip" class="w-full px-3 py-2 rounded-lg border border-gray-200 text-secondary text-base focus:ring-2 focus:ring-primary focus:border-primary bg-gray-50 transition-all" required>
                            </div>
                        </div>
                        <div class="mt-4">
                            <label for="country" class="block text-secondary text-sm font-semibold mb-1">Country</label>
                            <input type="text" id="country" class="w-full px-3 py-2 rounded-lg border border-gray-200 text-secondary text-base focus:ring-2 focus:ring-primary focus:border-primary bg-gray-50 transition-all" required>
                        </div>
                        <div class="mt-4">
                            <label for="country" class="block text-secondary text-sm font-semibold mb-1">Country</label>
                            <select id="country" class="w-full px-3 py-2 rounded-lg border border-gray-200 text-secondary text-base focus:ring-2 focus:ring-primary focus:border-primary bg-gray-50 transition-all" required>
                                <option value="" disabled selected>Select your country</option>
                                <option value="US">United States</option>
                                <option value="CA">Canada</option>
                                <option value="GB">United Kingdom</option>
                                <option value="AU">Australia</option>
                                <option value="NZ">New Zealand</option>
                            </select>
                        </div>
                    </div>

                    <!-- Payment Method -->
                    <div class="checkout-section bg-white rounded-2xl shadow-lg p-8 mb-8 border border-gray-100">
                        <h2 class="checkout-section-title text-2xl font-bold text-secondary mb-6 pb-2 border-b border-gray-100">Payment Method</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                            <div class="payment-method border rounded-lg p-4 cursor-pointer transition-all hover:bg-primary hover:bg-opacity-10 flex items-center gap-3 bg-primary bg-opacity-10 border-primary ring-2 ring-primary" data-method="credit-card">
                                <label class="flex items-center cursor-pointer w-full">
                                    <input type="radio" name="payment-method" value="credit-card" class="hidden" checked>
                                    <div class="w-5 h-5 rounded-full border-2 border-primary mr-3 flex items-center justify-center">
                                        <div class="w-3 h-3 rounded-full bg-primary"></div>
                                    </div>
                                    <div class="flex items-center text-secondary text-base font-semibold">
                                        <i class="ri-bank-card-line text-xl mr-2"></i>
                                        <span>Credit/Debit Card</span>
                                    </div>
                                </label>
                            </div>
                            <div class="payment-method border rounded-lg p-4 cursor-pointer transition-all hover:bg-primary hover:bg-opacity-10 flex items-center gap-3" data-method="paypal">
                                <label class="flex items-center cursor-pointer w-full">
                                    <input type="radio" name="payment-method" value="paypal" class="hidden">
                                    <div class="w-5 h-5 rounded-full border-2 border-gray-300 mr-3 flex items-center justify-center">
                                        <div class="w-3 h-3 rounded-full bg-transparent"></div>
                                    </div>
                                    <div class="flex items-center text-secondary text-base font-semibold">
                                        <i class="ri-paypal-line text-xl mr-2"></i>
                                        <span>PayPal</span>
                                    </div>
                                </label>
                            </div>
                        </div>
                        <!-- Credit Card Form (default) -->
                        <div id="credit-card-form">
                            <div class="mt-3">
                                <label for="card-name" class="block text-secondary text-sm font-semibold mb-1">Name on Card</label>
                                <input type="text" id="card-name" class="w-full px-3 py-2 rounded-lg border border-gray-200 text-secondary text-base focus:ring-2 focus:ring-primary focus:border-primary bg-gray-50 transition-all" required>
                            </div>
                            <div class="mt-3">
                                <label for="card-number" class="block text-secondary text-sm font-semibold mb-1">Card Number</label>
                                <div class="relative">
                                    <input type="text" id="card-number" class="w-full px-3 py-2 rounded-lg border border-gray-200 text-secondary text-base focus:ring-2 focus:ring-primary focus:border-primary bg-gray-50 transition-all pl-10" placeholder="0000 0000 0000 0000" required>
                                    <div class="absolute left-2 top-1/2 transform -translate-y-1/2">
                                        <i class="ri-bank-card-line text-gray-400"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-3 mt-3">
                                <div>
                                    <label for="card-expiry" class="block text-secondary text-sm font-semibold mb-1">Expiration Date</label>
                                    <input type="text" id="card-expiry" class="w-full px-3 py-2 rounded-lg border border-gray-200 text-secondary text-base focus:ring-2 focus:ring-primary focus:border-primary bg-gray-50 transition-all" placeholder="MM/YY" required>
                                </div>
                                <div>
                                    <label for="card-cvc" class="block text-secondary text-sm font-semibold mb-1">CVC</label>
                                    <input type="text" id="card-cvc" class="w-full px-3 py-2 rounded-lg border border-gray-200 text-secondary text-base focus:ring-2 focus:ring-primary focus:border-primary bg-gray-50 transition-all" placeholder="123" required>
                                </div>
                            </div>
                        </div>
                        <!-- PayPal Form (hidden by default) -->
                        <div id="paypal-form" class="hidden mt-3">
                            <p class="text-secondary mb-3">You will be redirected to PayPal to complete your payment.</p>
                        </div>
                    </div>

                    <!-- Order Notes (Optional) -->
                    <div class="checkout-section">
                        <h2 class="checkout-section-title">Order Notes (Optional)</h2>
                        <div class="mt-4">
                            <label for="order-notes" class="block text-secondary text-sm font-semibold mb-1">Add any special instructions or requests</label>
                            <textarea id="order-notes" class="w-full px-3 py-2 rounded-lg border border-gray-200 text-secondary text-base focus:ring-2 focus:ring-primary focus:border-primary bg-gray-50 transition-all" rows="3"></textarea>
                        </div>
                    </div>

                    <!-- Review Policy -->
                    <div class="checkout-section">
                        <div class="flex items-start mb-4">
                            <div class="flex items-center h-5 mt-1">
                                <input id="terms" type="checkbox" class="w-4 h-4 text-primary bg-gray-100 border-gray-300 focus:ring-primary" required>
                            </div>
                            <label for="terms" class="ml-3 text-sm text-secondary">
                                I agree to the <a href="#" class="text-primary hover:underline">Terms of Service</a> and <a href="#" class="text-primary hover:underline">Privacy Policy</a>
                            </label>
                        </div>
                        <div class="flex items-start">
                            <div class="flex items-center h-5 mt-1">
                                <input id="newsletter" type="checkbox" class="w-4 h-4 text-primary bg-gray-100 border-gray-300 focus:ring-primary">
                            </div>
                            <label for="newsletter" class="ml-3 text-sm text-secondary">
                                Sign me up for the newsletter to receive updates about new products and promotions
                            </label>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Order Summary -->
            <div class="w-full lg:w-1/3">
                <div class="sticky top-32">
                    <div class="cart-summary-card p-6 mb-6">
                        <h3 class="text-xl font-bold text-secondary mb-6">Order Summary</h3>

                        <div id="checkout-items" class="divide-y divide-gray-100 mb-6">
                            <!-- Cart items will be inserted here by JavaScript -->
                        </div>

                        <div class="space-y-4 mb-6">
                            <div class="flex justify-between">
                                <span class="text-secondary">Subtotal</span>
                                <span id="checkout-subtotal" class="text-secondary font-medium">$0.00</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-secondary">Shipping</span>
                                <span id="checkout-shipping" class="text-secondary font-medium">$5.99</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-secondary">Tax (7%)</span>
                                <span id="checkout-tax" class="text-secondary font-medium">$0.00</span>
                            </div>
                            <div class="border-t border-gray-200 pt-4 mt-4">
                                <div class="flex justify-between">
                                    <span class="text-secondary font-bold">Total</span>
                                    <span id="checkout-total" class="text-secondary font-bold">$5.99</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <button type="submit" id="place-order-button" class="w-full bg-primary text-secondary py-3 rounded-button font-medium flex items-center justify-center hover:brightness-110 transition-all">
                        <i class="ri-secure-payment-line mr-2"></i>
                        Place Order
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
</section>

<!-- Order Confirmation Modal (initially hidden) -->
<div id="order-confirmation" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4 shadow-xl transform transition-all">
        <div class="text-center">
            <div class="mb-4 text-center">
                <div class="mx-auto w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
                    <i class="ri-check-line text-4xl text-green-500"></i>
                </div>
            </div>
            <h2 class="text-2xl font-bold text-secondary mb-4">Thank You for Your Order!</h2>
            <p class="text-secondary mb-2">Your order has been placed successfully.</p>
            <p class="text-secondary font-bold mb-6">Order #<span id="order-number">12345</span></p>
            <p class="text-secondary text-sm mb-8">A confirmation email will be sent to your inbox shortly.</p>
            <div class="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3 justify-center">
                <a href="index.html" class="bg-primary text-secondary px-6 py-3 rounded-button font-medium inline-flex items-center justify-center hover:brightness-110 transition-all">
                    <i class="ri-home-line mr-2"></i>
                    Return to Home
                </a>
                <a href="products.html" class="border border-secondary text-secondary px-6 py-3 rounded-button font-medium inline-flex items-center justify-center hover:bg-secondary hover:text-white transition-all">
                    <i class="ri-store-2-line mr-2"></i>
                    Continue Shopping
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="bg-[#F8F4EC] pt-16 pb-8">
<div class="container mx-auto px-6">
<div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
<div>
<a href="index.html" class="inline-block mb-4">
<img src="Logo/better grain.svg" alt="Better Grain Logo" class="h-24 mb-0">
</a>
<p class="text-secondary opacity-80 text-sm leading-relaxed">Cultivating ancient grains for a healthier future and a thriving planet.</p>
<div class="flex space-x-4 mt-6">
<a href="#" class="text-secondary hover:text-primary"><i class="ri-facebook-fill ri-lg"></i></a>
<a href="#" class="text-secondary hover:text-primary"><i class="ri-instagram-line ri-lg"></i></a>
<a href="#" class="text-secondary hover:text-primary"><i class="ri-twitter-x-line ri-lg"></i></a>
</div>
</div>
<div>
<h4 class="font-bold text-secondary mb-4">Quick Links</h4>
<ul class="space-y-2">
<li><a href="index.html" class="text-secondary hover:text-primary text-sm">Home</a></li>
<li><a href="products.html" class="text-secondary hover:text-primary text-sm">Products</a></li>
<li><a href="about.html" class="text-secondary hover:text-primary text-sm">About Us</a></li>
<li><a href="farmers.html" class="text-secondary hover:text-primary text-sm">Our Farmers</a></li>
<li><a href="contact.html" class="text-secondary hover:text-primary text-sm">Contact</a></li>
</ul>
</div>
<div>
<h4 class="font-bold text-secondary mb-4">Resources</h4>
<ul class="space-y-2">
<li><a href="regenerative.html" class="text-secondary hover:text-primary text-sm">Regenerative Agriculture</a></li>
<li><a href="recipes.html" class="text-secondary hover:text-primary text-sm">Recipes & Guides</a></li>
<li><a href="faq.html" class="text-secondary hover:text-primary text-sm">FAQ</a></li>
</ul>
</div>
<div>
<h4 class="font-bold text-secondary mb-4">Contact Us</h4>
<address class="text-secondary opacity-80 text-sm not-italic space-y-2">
<p>123 Grain Lane, <br>Harvestville, CA 90210</p>
<p>Email: <a href="mailto:<EMAIL>" class="hover:text-primary"><EMAIL></a></p>
<p>Phone: <a href="tel:+1234567890" class="hover:text-primary">(*************</a></p>
</address>
</div>
</div>
<div class="border-t border-secondary border-opacity-20 pt-8 text-center">
<p class="text-secondary opacity-70 text-sm">&copy; 2024 Better Grain. All Rights Reserved.</p>
</div>
</div>
</footer>

<!-- JavaScript -->
<script src="js/main.js"></script>
<script src="js/cart.js"></script>
<script>
// Checkout page specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const emptyCheckoutError = document.getElementById('empty-checkout-error');
    const checkoutContent = document.getElementById('checkout-content');
    const checkoutItems = document.getElementById('checkout-items');
    const checkoutSubtotalEl = document.getElementById('checkout-subtotal');
    const checkoutTaxEl = document.getElementById('checkout-tax');
    const checkoutTotalEl = document.getElementById('checkout-total');
    const placeOrderBtn = document.getElementById('place-order-button');
    const orderConfirmation = document.getElementById('order-confirmation');
    const orderNumber = document.getElementById('order-number');
    const checkoutForm = document.getElementById('checkout-form');

    // Payment method selection
    const paymentMethods = document.querySelectorAll('.payment-method');
    const creditCardForm = document.getElementById('credit-card-form');
    const paypalForm = document.getElementById('paypal-form');

    // Shipping cost (fixed)
    const shippingCost = 5.99;
    const taxRate = 0.07; // 7% tax rate

    // Initialize checkout page
    function initCheckout() {
        const cart = CartManager.getCart();

        // Show error if cart is empty
        if (cart.length === 0) {
            emptyCheckoutError.classList.remove('hidden');
            checkoutContent.classList.add('hidden');
            return;
        }

        emptyCheckoutError.classList.add('hidden');
        checkoutContent.classList.remove('hidden');

        // Render order summary
        renderOrderSummary();

        // Set up payment method switching
        setupPaymentMethodSelection();

        // Set up place order button
        setupPlaceOrderButton();
    }

    // Render the order summary
    function renderOrderSummary() {
        const cart = CartManager.getCart();

        // Clear existing items
        checkoutItems.innerHTML = '';

        // Add each cart item
        cart.forEach(item => {
            const itemTotal = item.price * item.quantity;
            const itemEl = document.createElement('div');
            itemEl.className = 'py-3 flex items-center justify-between';
            itemEl.innerHTML = `
                <div class="flex items-center">
                    <div class="w-12 h-12 rounded overflow-hidden mr-3">
                        <img src="${item.image}" alt="${item.name}" class="w-full h-full object-cover">
                    </div>
                    <div>
                        <h4 class="text-secondary font-medium">${item.name}</h4>
                        <div class="text-sm text-secondary opacity-80">Qty: ${item.quantity}</div>
                    </div>
                </div>
                <div class="text-secondary font-medium">$${itemTotal.toFixed(2)}</div>
            `;
            checkoutItems.appendChild(itemEl);
        });

        // Calculate and update totals
        const subtotal = CartManager.calculateTotal();
        const tax = subtotal * taxRate;
        const total = subtotal + tax + shippingCost;

        checkoutSubtotalEl.textContent = `$${subtotal.toFixed(2)}`;
        checkoutTaxEl.textContent = `$${tax.toFixed(2)}`;
        checkoutTotalEl.textContent = `$${total.toFixed(2)}`;
    }

    // Set up payment method selection
    function setupPaymentMethodSelection() {
        paymentMethods.forEach(method => {
            method.addEventListener('click', function() {
                // Remove selected class from all methods
                paymentMethods.forEach(m => {
                    m.classList.remove('selected');
                    m.querySelector('input').checked = false;
                    m.querySelector('.w-3').classList.remove('bg-primary');
                    m.querySelector('.w-3').classList.add('bg-transparent');
                    m.querySelector('.w-5').classList.remove('border-primary');
                    m.querySelector('.w-5').classList.add('border-gray-300');
                });

                // Add selected class to clicked method
                this.classList.add('selected');
                this.querySelector('input').checked = true;
                this.querySelector('.w-3').classList.add('bg-primary');
                this.querySelector('.w-3').classList.remove('bg-transparent');
                this.querySelector('.w-5').classList.add('border-primary');
                this.querySelector('.w-5').classList.remove('border-gray-300');

                // Show/hide appropriate form
                const methodName = this.dataset.method;
                if (methodName === 'credit-card') {
                    creditCardForm.classList.remove('hidden');
                    paypalForm.classList.add('hidden');
                } else if (methodName === 'paypal') {
                    creditCardForm.classList.add('hidden');
                    paypalForm.classList.remove('hidden');
                }
            });
        });
    }

    // Set up place order button
    function setupPlaceOrderButton() {
        if (placeOrderBtn) {
            placeOrderBtn.addEventListener('click', function(e) {
                e.preventDefault();

                // Validate form fields are filled
                const formValid = checkoutForm.checkValidity();
                const termsChecked = document.getElementById('terms').checked;

                if (formValid && termsChecked) {
                    // Form is valid, show confirmation dialog

                    // Generate random order number
                    const orderNum = Math.floor(10000 + Math.random() * 90000);
                    orderNumber.textContent = orderNum;

                    // Clear cart
                    CartManager.clearCart();

                    // Show confirmation
                    orderConfirmation.classList.remove('hidden');

                    // Close confirmation when clicking outside
                    orderConfirmation.addEventListener('click', function(e) {
                        if (e.target === orderConfirmation) {
                            orderConfirmation.classList.add('hidden');
                        }
                    });
                } else if (!termsChecked) {
                    // Alert if terms not checked
                    alert("Please agree to the Terms of Service to proceed.");
                } else {
                    // Trigger browser's form validation
                    checkoutForm.reportValidity();
                }
            });
        }
    }

    // Initialize checkout
    initCheckout();

    // Update cart display when storage changes (in case another tab updates it)
    window.addEventListener('storage', function(e) {
        if (e.key === 'betterGrainCart') {
            initCheckout();
        }
    });
});
</script>
</body>
</html>