document.addEventListener('DOMContentLoaded', () => {
    const menuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    const closeButton = document.getElementById('mobile-menu-close-button');

    if (menuButton && mobileMenu) {
        menuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('is-open');
            document.body.classList.toggle('mobile-menu-open');
        });
    }

    if (closeButton && mobileMenu) {
        closeButton.addEventListener('click', () => {
            mobileMenu.classList.remove('is-open');
            document.body.classList.remove('mobile-menu-open');
        });
    }

    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href.startsWith('#') && href.length > 1) { 
                const targetId = href.substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    e.preventDefault();
                    if (mobileMenu && mobileMenu.classList.contains('is-open')) {
                         mobileMenu.classList.remove('is-open');
                         document.body.classList.remove('mobile-menu-open');
                    }
                    
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                } 
            } 
        });
    });

    // Fade-in Animation on Scroll (for elements with class .fade-in, specific to about.html but safe globally)
    const fadeElements = document.querySelectorAll('.fade-in');
    if (fadeElements.length > 0) { // Check if any fade elements exist on the page
        const fadeInOnScroll = () => {
            fadeElements.forEach(element => {
                // Check if element is already visible to prevent re-triggering
                if (!element.classList.contains('visible')) {
                    const elementTop = element.getBoundingClientRect().top;
                    const windowHeight = window.innerHeight;
                    // Trigger animation when element is about 150px from bottom of viewport
                    const elementVisibleThreshold = 150; 
                    
                    if (elementTop < windowHeight - elementVisibleThreshold) {
                        element.classList.add('visible');
                    }
                }
            });
        };
        
        // Add scroll listener and trigger initial check
        window.addEventListener('scroll', fadeInOnScroll);
        fadeInOnScroll(); // Run on load in case elements are already in view
    }

}); 