<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Better Grain - Contact Us</title>
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [
    {
      "@type": "ListItem",
      "position": 1,
      "name": "Home",
      "item": "https://spedagrame.com/"
    },
    {
      "@type": "ListItem",
      "position": 2,
      "name": "Contact",
      "item": "https://spedagrame.com/contact.html"
    }
  ]
}
</script>
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "ContactPage",
  "name": "Contact Better Grain - Inquiries, Support, and Wholesale",
  "description": "Reach out to Better Grain for questions about our Einkorn products, regenerative farming practices, wholesale accounts, or general inquiries. We look forward to connecting with you.",
  "url": "https://spedagrame.com/contact.html",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://spedagrame.com/contact.html"
  },
  "publisher": {
    "@type": "Organization",
    "name": "Better Grain",
    "logo": {
      "@type": "ImageObject",
      "url": "https://spedagrame.com/Logo/better%20grain.svg"
    },
    "contactPoint": [
      {
        "@type": "ContactPoint",
        "telephone": "******-123-4567",
        "contactType": "customer support", // General customer service
        "areaServed": "CA", // Assuming Canada, adjust if US or broader
        "availableLanguage": ["English"]
      },
      {
        "@type": "ContactPoint",
        "email": "<EMAIL>",
        "contactType": "customer support",
        "availableLanguage": ["English"]
      },
      {
        "@type": "ContactPoint",
        "contactType": "sales", // For wholesale inquiries
        "email": "<EMAIL>", // Placeholder, use actual if available
        "areaServed": "CA",
        "availableLanguage": ["English"]
      }
    ]
  }
}
</script>
<script src="https://cdn.tailwindcss.com/3.4.16"></script>
<script>
    tailwind.config = {
        theme: {
            extend: {
                fontFamily: {
                    'merriweather': ['Merriweather', 'serif'],
                    'lato': ['Lato', 'sans-serif']
                },
                colors: {
                    primary: '#b4722c', // VIS Golden Grain (CTAs, Links, Key Icons)
                    secondary: '#4c312a', // VIS Rich Soil (Headings, Main Text)
                    'page-bg': '#FAF8F5', // VIS Light Warm Cream (Default Page Background)
                    'terracotta-earth': '#9e5f30', // VIS Terracotta Earth (Secondary Buttons, Subtle Accents)
                    'golden-grain': '#b4722c', // VIS Golden Grain
                    'light-warm-cream': '#FAF8F5', // VIS Light Warm Cream (Text on Dark Backgrounds)
                    'medium-brown': '#8B7355', // Legacy support
                },
                borderRadius: {
                    'none': '0px',
                    'sm': '4px',
                    DEFAULT: '8px',
                    'md': '12px',
                    'lg': '16px',
                    'xl': '20px',
                    '2xl': '24px',
                    '3xl': '32px',
                    'full': '9999px',
                    'button': '8px'
                }
            }
        }
    }
</script>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Open+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Lato:wght@400;600&family=Merriweather:wght@400;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
<link rel="stylesheet" href="css/style.css">
<style>
    /* Page-specific styles for contact.html */
    .hero-bg {
        background-image: url('https://readdy.ai/api/search-image?query=A%2520warm%2520and%2520inviting%2520image%2520of%2520a%2520desk%2520with%2520contact%2520elements.%2520Could%2520include%2520a%2520stylized%2520envelope%252C%2520a%2520vintage-style%2520telephone%252C%2520or%2520a%2520laptop%2520showing%2520an%2520email%2520interface.%2520The%2520background%2520should%2520be%2520subtly%2520textured%2520%28like%2520the%2520grain%2520texture%2520used%2520elsewhere%29%2520and%2520the%2520lighting%2520soft%2520and%2520natural.%2520The%2520left%2520side%2520could%2520fade%2520to%2520allow%2520text%2520overlay.%2520Color%2520palette%2520should%2520match%2520the%2520brand%2520%28primary%2520%26%2520secondary%2520colors%29.&width=1600&height=600&seq=1&orientation=landscape');
        background-position: center center;
        background-repeat: no-repeat;
        background-size: cover;
    }
</style>
</head>
<body class="min-h-screen bg-page-bg">
<!-- Header & Navigation -->
<header class="fixed w-full bg-white bg-opacity-95 shadow-sm z-50">
    <div class="container mx-auto px-6 py-4 flex justify-between items-center">
        <a href="index.html" class="flex items-center">
            <img src="Logo/better grain.svg" alt="Better Grain Logo" class="h-24">
        </a>
        <nav class="hidden md:flex space-x-8">
            <a href="index.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Home</a>
            <a href="products.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Products</a>
            <a href="about.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">About Us</a>
            <a href="farmers.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Farmers</a>
            <div class="relative group">
                <button class="nav-link text-secondary hover:text-primary font-medium transition-colors flex items-center">
                    Resources <i class="ri-arrow-down-s-line ml-1"></i>
                </button>
                <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 invisible group-hover:visible">
                    <a href="regenerative.html" class="block px-4 py-2 text-sm text-secondary hover:bg-primary hover:bg-opacity-10 hover:text-primary">Regenerative</a>
                    <a href="recipes.html" class="block px-4 py-2 text-sm text-secondary hover:bg-primary hover:bg-opacity-10 hover:text-primary">Recipes</a>
                </div>
            </div>
            <a href="contact.html" class="nav-link text-primary font-medium transition-colors active">Contact</a>
        </nav>
        <!-- Mobile Menu Button -->
        <div class="md:hidden">
            <button id="mobile-menu-button" class="text-secondary focus:outline-none">
                <i class="ri-menu-line ri-lg"></i>
            </button>
        </div>
    </div>
    <!-- Mobile Menu Drawer -->
    <div id="mobile-menu" class="fixed inset-y-0 right-0 w-64 bg-white shadow-lg p-6 z-50 md:hidden transform translate-x-full transition-transform duration-300 ease-in-out">
        <div class="flex justify-between items-center mb-8">
            <img src="Logo/better grain.svg" alt="Better Grain Logo" class="h-24">
            <button id="mobile-menu-close-button" class="text-secondary focus:outline-none">
                <i class="ri-close-line ri-lg"></i>
            </button>
        </div>
        <nav class="flex flex-col space-y-4">
            <a href="index.html" class="text-secondary hover:text-primary font-medium">Home</a>
            <a href="products.html" class="text-secondary hover:text-primary font-medium">Products</a>
            <a href="about.html" class="text-secondary hover:text-primary font-medium">About Us</a>
            <a href="farmers.html" class="text-secondary hover:text-primary font-medium">Farmers</a>
            <div>
                <span class="font-medium text-secondary">Resources</span>
                <div class="flex flex-col space-y-2 pl-4 mt-2">
                    <a href="regenerative.html" class="text-sm text-secondary hover:text-primary">Regenerative</a>
                    <a href="recipes.html" class="text-sm text-secondary hover:text-primary">Recipes</a>
                </div>
            </div>
            <a href="contact.html" class="text-secondary hover:text-primary font-medium">Contact</a>
        </nav>
    </div>
</header>

<!-- Hero Section -->
<section class="pt-32 pb-12 hero-bg grain-texture">
    <div class="container mx-auto px-6">
        <div class="w-full md:w-1/2 bg-white bg-opacity-90 p-8 md:p-12 rounded-lg shadow-lg">
             <h1 class="text-4xl md:text-5xl font-bold text-secondary mb-6">Connect with Better Grain</h1>
            <p class="text-xl text-secondary opacity-80 mb-8">Have questions about our Einkorn products, regenerative farming, or wholesale inquiries? Our team is here to help. Reach out to us – we look forward to connecting with you!</p>
        </div>
    </div>
</section>

<!-- Contact Details & Form Section -->
<section id="contact" class="py-20 bg-white grain-texture">
    <div class="container mx-auto px-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-16">
            <!-- Contact Form -->
            <div>
                <h2 class="text-3xl md:text-4xl font-bold text-secondary mb-6">Send Us a Message</h2>
                <p class="text-secondary opacity-80 mb-8">Fill out the form below, and a member of our team will get back to you as soon as possible.</p>

                <form class="space-y-6">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-secondary mb-2">Name</label>
                            <input type="text" id="name" class="w-full px-4 py-3 !rounded-button border border-gray-200 shadow-sm focus:border-primary focus:ring-primary text-secondary" required>
                        </div>
                        <div>
                            <label for="email" class="block text-sm font-medium text-secondary mb-2">Email</label>
                            <input type="email" id="email" class="w-full px-4 py-3 !rounded-button border border-gray-200 shadow-sm focus:border-primary focus:ring-primary text-secondary" required>
                        </div>
                    </div>
                     <div>
                        <label for="phone" class="block text-sm font-medium text-secondary mb-2">Phone <span class="text-xs opacity-70">(Optional)</span></label>
                        <input type="tel" id="phone" class="w-full px-4 py-3 !rounded-button border border-gray-200 shadow-sm focus:border-primary focus:ring-primary text-secondary">
                    </div>
                    <div>
                        <label for="subject" class="block text-sm font-medium text-secondary mb-2">Subject</label>
                        <input type="text" id="subject" class="w-full px-4 py-3 !rounded-button border border-gray-200 shadow-sm focus:border-primary focus:ring-primary text-secondary" required>
                    </div>
                    <div>
                        <label for="message" class="block text-sm font-medium text-secondary mb-2">Message</label>
                        <textarea id="message" rows="5" class="w-full px-4 py-3 !rounded-button border border-gray-200 shadow-sm focus:border-primary focus:ring-primary text-secondary" required></textarea>
                    </div>
                    <button type="submit" class="bg-primary text-secondary px-8 py-3 !rounded-button font-semibold whitespace-nowrap shadow-md hover:brightness-110">Send Message</button>
                </form>
            </div>

            <!-- Contact Information -->
            <div>
                 <h2 class="text-3xl md:text-4xl font-bold text-secondary mb-6">Contact Information</h2>
                  <p class="text-secondary opacity-80 mb-8">You can also reach us directly or find us on social media. We aim to respond to all inquiries within 24-48 business hours.</p>

                <div class="bg-page-bg p-8 rounded-lg shadow-md space-y-6 mb-8">
                    <div class="flex items-start">
                        <div class="w-10 h-10 flex items-center justify-center bg-primary bg-opacity-10 rounded-full mr-4 shrink-0 mt-1">
                            <i class="ri-map-pin-line text-primary ri-lg"></i>
                        </div>
                        <div>
                            <h4 class="font-bold text-secondary text-lg mb-1">Address</h4>
                            <p class="text-secondary opacity-80">Better Grain Farm</p>
                            <p class="text-secondary opacity-80">1234 Harvest Lane,</p>
                            <p class="text-secondary opacity-80">Wheatfield, CA 95678</p>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="w-10 h-10 flex items-center justify-center bg-primary bg-opacity-10 rounded-full mr-4 shrink-0 mt-1">
                            <i class="ri-phone-line text-primary ri-lg"></i>
                        </div>
                        <div>
                            <h4 class="font-bold text-secondary text-lg mb-1">Phone</h4>
                            <a href="tel:+15551234567" class="text-secondary opacity-80 hover:text-primary transition-colors">(*************</a>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="w-10 h-10 flex items-center justify-center bg-primary bg-opacity-10 rounded-full mr-4 shrink-0 mt-1">
                            <i class="ri-mail-line text-primary ri-lg"></i>
                        </div>
                        <div>
                            <h4 class="font-bold text-secondary text-lg mb-1">Email</h4>
                            <a href="mailto:<EMAIL>" class="text-secondary opacity-80 hover:text-primary transition-colors"><EMAIL></a>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="w-10 h-10 flex items-center justify-center bg-primary bg-opacity-10 rounded-full mr-4 shrink-0 mt-1">
                            <i class="ri-time-line text-primary ri-lg"></i>
                        </div>
                        <div>
                            <h4 class="font-bold text-secondary text-lg mb-1">Farm Store Hours</h4>
                            <p class="text-secondary opacity-80">Thursday - Sunday: 10am - 4pm</p>
                            <p class="text-secondary opacity-80 text-sm">(Please call ahead for large groups)</p>
                        </div>
                    </div>
                     <div class="flex items-start">
                        <div class="w-10 h-10 flex items-center justify-center bg-primary bg-opacity-10 rounded-full mr-4 shrink-0 mt-1">
                            <i class="ri-share-line text-primary ri-lg"></i>
                        </div>
                        <div>
                            <h4 class="font-bold text-secondary text-lg mb-2">Follow Us</h4>
                             <div class="flex space-x-3">
                                <a href="#" class="w-8 h-8 flex items-center justify-center bg-white bg-opacity-50 rounded-full hover:bg-primary hover:text-secondary transition-colors text-secondary">
                                    <i class="ri-instagram-line"></i>
                                </a>
                                <a href="#" class="w-8 h-8 flex items-center justify-center bg-white bg-opacity-50 rounded-full hover:bg-primary hover:text-secondary transition-colors text-secondary">
                                    <i class="ri-facebook-fill"></i>
                                </a>
                                <a href="#" class="w-8 h-8 flex items-center justify-center bg-white bg-opacity-50 rounded-full hover:bg-primary hover:text-secondary transition-colors text-secondary">
                                    <i class="ri-twitter-x-line"></i>
                                </a>
                                <a href="#" class="w-8 h-8 flex items-center justify-center bg-white bg-opacity-50 rounded-full hover:bg-primary hover:text-secondary transition-colors text-secondary">
                                    <i class="ri-pinterest-line"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Social Media Links -->
                <div class="bg-page-bg p-8 rounded-lg shadow-md">
                    <h3 class="text-2xl font-bold text-secondary mb-4">Follow Our Journey</h3>
                    <p class="text-secondary opacity-80 mb-6">Stay updated on our latest news, products, and regenerative farming stories by connecting with us on social media:</p>
                    <div class="flex space-x-6">
                        <a href="#YOUR_FACEBOOK_LINK" target="_blank" class="text-secondary hover:text-primary transition-colors" aria-label="Facebook">
                            <i class="ri-facebook-circle-fill ri-2x"></i>
                        </a>
                        <a href="#YOUR_INSTAGRAM_LINK" target="_blank" class="text-secondary hover:text-primary transition-colors" aria-label="Instagram">
                            <i class="ri-instagram-fill ri-2x"></i>
                        </a>
                        <a href="#YOUR_TWITTER_LINK" target="_blank" class="text-secondary hover:text-primary transition-colors" aria-label="Twitter">
                            <i class="ri-twitter-x-fill ri-2x"></i>
                        </a>
                         <a href="#YOUR_PINTEREST_LINK" target="_blank" class="text-secondary hover:text-primary transition-colors" aria-label="Pinterest">
                            <i class="ri-pinterest-fill ri-2x"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Callout Section -->
<section class="py-20 bg-page-bg grain-texture">
    <div class="container mx-auto px-6">
        <div class="max-w-3xl mx-auto text-center bg-white p-10 rounded-lg shadow-lg">
            <div class="w-20 h-20 flex items-center justify-center bg-primary bg-opacity-10 rounded-full mx-auto mb-6">
                <i class="ri-question-answer-line ri-2x text-primary"></i>
            </div>
            <h2 class="text-3xl font-bold text-secondary mb-6">Have More Questions?</h2>
            <p class="text-lg text-secondary opacity-80 mb-8">Check out our Frequently Asked Questions page for quick answers to common inquiries about Einkorn, our products, shipping, and more.</p>
            <a href="faq.html" class="inline-flex items-center bg-secondary text-white px-8 py-3 !rounded-button font-semibold whitespace-nowrap shadow-md hover:bg-opacity-90 transition-colors">
                View FAQs (Coming Soon!)
                <i class="ri-arrow-right-line ml-2"></i>
            </a>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="py-10 bg-page-bg">
    <div class="container mx-auto px-6">
        <h2 class="text-3xl font-bold text-secondary mb-8 text-center">Find Us</h2>
        <div class="rounded-lg overflow-hidden shadow-lg h-96">
            <!-- Placeholder for map integration -->
            <img src="https://public.readdy.ai/gen_page/map_placeholder_1280x720.png" alt="Farm Location Map" class="w-full h-full object-cover">
            <!-- In a real implementation, replace the above img with an interactive map embed (e.g., Google Maps iframe) -->
            <!-- Example Google Maps iframe (replace with actual embed code): -->
            <!-- <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d...." width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe> -->
        </div>
    </div>
</section>


<!-- Footer -->
<footer class="bg-page-bg pt-16 pb-8">
    <div class="container mx-auto px-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
            <div>
                <a href="index.html" class="inline-block mb-4">
                    <img src="Logo/better grain.svg" alt="Better Grain Logo" class="h-24">
                </a>
                <p class="text-secondary opacity-80 text-sm leading-relaxed">Cultivating ancient grains for a healthier future and a thriving planet.</p>
                <div class="flex space-x-4 mt-6">
                    <a href="#" class="text-secondary hover:text-primary"><i class="ri-facebook-fill ri-lg"></i></a>
                    <a href="#" class="text-secondary hover:text-primary"><i class="ri-instagram-line ri-lg"></i></a>
                    <a href="#" class="text-secondary hover:text-primary"><i class="ri-twitter-x-line ri-lg"></i></a>
                </div>
            </div>
            <div>
                <h4 class="font-bold text-secondary mb-4">Quick Links</h4>
                <ul class="space-y-2">
                    <li><a href="index.html" class="text-secondary hover:text-primary text-sm">Home</a></li>
                    <li><a href="products.html" class="text-secondary hover:text-primary text-sm">Products</a></li>
                    <li><a href="about.html" class="text-secondary hover:text-primary text-sm">About Us</a></li>
                    <li><a href="farmers.html" class="text-secondary hover:text-primary text-sm">Our Farmers</a></li>
                    <li><a href="contact.html" class="text-secondary hover:text-primary text-sm">Contact</a></li>
                </ul>
            </div>
            <div>
                <h4 class="font-bold text-secondary mb-4">Resources</h4>
                <ul class="space-y-2">
                    <li><a href="regenerative.html" class="text-secondary hover:text-primary text-sm">Regenerative Agriculture</a></li>
                    <li><a href="recipes.html" class="text-secondary hover:text-primary text-sm">Recipes & Guides</a></li>
                    <li><a href="faq.html" class="text-secondary hover:text-primary text-sm">FAQ</a></li>
                </ul>
            </div>
            <div>
                <h4 class="font-bold text-secondary mb-4">Contact Us</h4>
                <address class="text-secondary opacity-80 text-sm not-italic space-y-2">
                    <p>123 Grain Lane, <br>Harvestville, CA 90210</p>
                    <p>Email: <a href="mailto:<EMAIL>" class="hover:text-primary"><EMAIL></a></p>
                    <p>Phone: <a href="tel:+1234567890" class="hover:text-primary">(*************</a></p>
                </address>
            </div>
        </div>
        <div class="border-t border-secondary border-opacity-20 pt-8 text-center">
            <p class="text-secondary opacity-70 text-sm">&copy; 2024 Better Grain. All Rights Reserved.</p>
        </div>
    </div>
</footer>

<script src="js/main.js"></script>
</body>
</html>