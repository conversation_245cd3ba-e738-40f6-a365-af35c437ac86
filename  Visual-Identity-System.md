Objective: Generate all website pages (including but not limited to Homepage, Products Page, Product Detail Pages, About Us Page, Farmers Page, Resources/Blog Page, Contact Page) for 'BetterGrain' adhering strictly to the following Visual Identity System. The brand personality is warm, authentic, trustworthy, natural, and premium-quality, focusing on einkorn wheat and sustainable, mindful farming.
I. Overall Page Structure & Layout:
Main Content Container:
All primary page content (text, main images, cards, etc., excluding full-width hero sections or full-width feature section backgrounds) should reside within a central content area with a max-width of 1200px, centered on the page (margin: 0 auto;).
This main content area should have consistent internal side padding of 30px.
Background Color (Default):
The default page background color should be a light, warm cream: #FAF8F5.
Vertical Spacing System (Based on an 8px grid for harmony):
Large spacing between major page sections: 80px - 100px.
Medium spacing (e.g., below section headlines, between distinct content blocks within a section): 32px - 40px.
Small spacing (e.g., between an icon and its title, paragraph margins): 16px - 24px.
Line height for body text: 1.7.
Paragraph bottom margin: 20px.
Alignment:
Default text alignment for body copy and most headlines within the main content container: Left.
Centered alignment can be used intentionally for:
Standalone main page titles/hero headlines.
Short, impactful text blocks if they are the primary element of a section (like the "Our Journey..." intro, if kept centered).
Content within cards (like icons/titles in "Our Commitment" pillars).
II. Color Palette & Usage:
Primary Dark (Text, Headlines): Rich Soil - #4c312a
Primary Light (Backgrounds): Light Cream - #FAF8F5
Primary Warm Accent (CTAs, Links, Icons, Highlights): Golden Grain - #b4722c
Secondary Warm Accent (Secondary Buttons, Subtle BG Tints, Alt Icons): Terracotta Earth - #9e5f30
Black (Use Sparingly - Fine Print): #000000
Rules:
Ensure WCAG AA contrast for all text against its background.
Use accents purposefully to draw attention to key actions or information.
III. Typography System:
Primary Heading Font: [Specify Your Chosen Heading Font Name, e.g., "Merriweather"]
Body Text Font: [Specify Your Chosen Body Font Name, e.g., "Lato"]
H1 (Main Page Titles):
Font: [Heading Font Name], Weight: Bold (700), Size: 56px (desktop, scale responsively), Color: #4c312a (on light BG) / #FAF8F5 (on dark BG/overlay), Line Height: 1.2.
H2 (Main Section Titles):
Font: [Heading Font Name], Weight: Bold (700), Size: 40px (desktop, scale responsively), Color: #4c312a.
Accent: Optional thin (2px) underline in #b4722c, width 75% of text, centered below text, padding-bottom: 8px.
margin-bottom: 32px.
H3 (Sub-Section Titles/Card Titles):
Font: [Heading Font Name], Weight: Semibold (600), Size: 28px (desktop, scale responsively), Color: #4c312a.
margin-bottom: 16px.
Body Text (Paragraphs):
Font: [Body Font Name], Weight: Regular (400), Size: 18px (desktop, scale responsively), Color: #4c312a, Line Height: 1.7.
max-width: 70ch (for single column text blocks).
margin-bottom: 20px.
Links (Inline):
Color: #b4722c. Underline on hover/focus.
Lists (Bullet Points):
Bullet Icon/Color: Use a custom small dot or einkorn sheaf icon colored #b4722c.
Text: Color #4c312a.
Highlighted terms within list items: font-weight: bold; color #4c312a.
Drop Caps (for introductory story paragraphs like "Our Journey..."):
First letter: Font [Heading Font Name or a distinct Serif if body is Sans-Serif], Size: 3em, Color: #b4722c, float: left, margin-right: 0.1em, line-height: 0.8.
IV. Logo Usage:
Primary: Horizontal lockup. Top-left in header, prominent in footer.
Colors: Use standard color version on light backgrounds (#FAF8F5). Use a reversed all-light-cream version or single-color #b4722c version on dark backgrounds.
Clear Space: Maintain clear space equivalent to the height of the "B" in "BetterGrain" around the logo.
Favicon: Simplified grain sheaf "G".
V. Imagery Style:
Tone: Warm, authentic, natural light, inviting, wholesome. Focus on einkorn, natural processes, healthy food, and connection to the earth.
Treatment: Apply subtle rounded corners (border-radius: 12px;) to standalone images. Optional very soft box-shadow: 0px 8px 25px rgba(76, 49, 42, 0.1); for images on colored section backgrounds.
VI. Iconography System:
Style: Custom-designed icons with an organic, natural, warm feel. Consistent line weight or fill style.
Color: Primarily #b4722c (Golden Grain) or #9e5f30 (Terracotta Earth).
Examples (Conceptual - AI should generate appropriate unique icons):
"Better Grain" pillar: Stylized einkorn wheat head.
"Better Health" pillar: Heart with an integrated einkorn sprout.
"Better Planet" pillar: Hands cradling soil with a young plant.
"Nature's Original Wheat" advantage: Ancient grain symbol.
"Gentle on Digestion" advantage: Soothing wave/feather.
"Nutrient Powerhouse" advantage: Radiant energy/strength symbol with grain.
"Deliciously Nutty Flavor" advantage: Stylized taste/baked good icon.
VII. UI Element Styling:
Buttons - Primary CTA (e.g., "Explore Our Products," "Add to Cart"):
Background: #b4722c (Golden Grain).
Text: Light Cream (#FAF8F5), Font: [Body Font Name or Heading Font Name - be consistent], Weight: Semibold (600), Size: 18px.
Padding: 15px 30px.
border-radius: 8px.
Hover: Background slightly darker (e.g., #a56A20).
Buttons - Secondary (e.g., "Learn More," "View All Products"):
Background: Transparent.
Border: 2px solid #9e5f30 (Terracotta Earth).
Text: #9e5f30, Font: [Same as Primary Button], Weight: Semibold (600), Size: 18px.
Padding: 13px 28px (slightly less than primary due to border).
border-radius: 8px.
Hover: Background #F9F5F0 (very light tint of Terracotta Earth), Text #4c312a.
Navigation Links (Header):
Text Color: #4c312a.
Hover/Active State: Text color #b4722c, optional thin (2px) underline in #b4722c below the text.
Cards (e.g., "Our Commitment" pillars, Product Cards):
Background: White (#FFFFFF) or very light cream (#FAF8F5 if page BG is slightly darker, or vice-versa for contrast).
border-radius: 12px.
box-shadow: 0px 6px 20px rgba(76, 49, 42, 0.08); (very soft, subtle).
Consistent internal padding (e.g., 30px).
Clear text hierarchy within cards (H3 for title, body text for description).
VIII. Specific Section Styles to Replicate/Adapt:
Hero Section (Homepage): Full-width image background. Text block (H1, paragraph) left-aligned on a semi-transparent dark overlay (rgba(76, 49, 42, 0.2)) or with sufficient text shadow for clarity. Primary CTA button. Trust signals below CTA.
"Our Commitment" Section (Homepage): H2 title. Three cards in a row, styled as per Card guidelines.
"Our Journey to Better Grains" Section (Homepage):
Within main content container, text block left-aligned.
H2 title with #b4722c underline.
Drop cap (#b4722c) for the first paragraph.
Constrained paragraph width (max-width: 70ch).
"The Gentle Power of Ancient Grain" Section (Homepage - "Contained Feature Section" style):
Full-width section wrapper with a distinct very light warm background (e.g., #F7F3EE).
Inside this wrapper, content (H2, paragraphs, bullets, image) within the 1200px main content area, laid out in two columns (Text left, Image right).
Text column: H2 (#4c312a + #b4722c underline), Paragraphs (#4c312a), Bullets (dots #b4722c, text #4c312a, highlights bold #4c312a).
Image column: Image with border-radius: 12px; and box-shadow: 0px 8px 25px rgba(76, 49, 42, 0.1);.
Generous vertical padding for the section wrapper (e.g., 80px).
"Experience the Einkorn Advantage" Section (Homepage): H2 title. Four cards in a row, similar to "Our Commitment" pillars.
IX. Responsive Design:
All elements must be fully responsive.
Typography scales appropriately for smaller screens.
Multi-column layouts stack gracefully on mobile (e.g., two columns become single column).
Navigation adapts to a mobile-friendly menu (e.g., hamburger icon).
Tap targets (buttons, links) are adequately sized for touchscreens.
X. Accessibility:
Ensure sufficient color contrast for all text.
Provide descriptive alt text for all images.
Ensure keyboard navigability.
Use semantic HTML.
