# Better Grain Website

This repository contains the frontend code for the Better Grain website, a static HTML, CSS, and JavaScript project showcasing the company's products and mission.

## Project Structure

```
better-grain/
├── css/              # Stylesheets
│   └── style.css
├── Images/           # Image assets (if any used directly)
├── js/               # JavaScript files
│   └── main.js
├── Logo/             # Logo files
│   └── better grain.svg
├── partials/         # HTML partials/includes (if any)
├── about.html        # About Us page
├── contact.html      # Contact page
├── farmers.html      # Farmers page
├── index.html        # Homepage
├── products.html     # Products page
├── README.md         # This file
├── ... (other pages/files)
```

## Setup

This is a static website project. No complex build steps or dependencies are required beyond a web browser.

1.  Clone or download this repository.
2.  Open any `.html` file directly in your web browser to view the pages.

## Local Development Server

To view the website locally and enable easy testing (especially on mobile devices on the same network), you can run a simple Python HTTP server from the root directory of the project (`better-grain/`).

1.  **Navigate to the project directory:**
    Open your terminal or command prompt and change the directory to where you cloned/downloaded the `better-grain` project.
    ```bash
    cd path/to/better-grain
    ```

2.  **Start the Python HTTP server:**
    Make sure you have Python 3 installed. Run the following command:
    ```bash
    python3 -m http.server 3000 --bind 0.0.0.0
    ```
    *   `3000` specifies the port number.
    *   `--bind 0.0.0.0` makes the server accessible from other devices on your local network (like your mobile phone).

3.  **Access the website:**
    *   **On your computer:** Open your web browser and go to `http://localhost:3000` or `http://127.0.0.1:3000`.
    *   **On your mobile device (or other devices on the same network):**
        *   Find your computer's local IP address (e.g., on Linux/macOS use `ip addr` or `ifconfig`, on Windows use `ipconfig`). It usually looks something like `192.168.1.X` or `10.0.0.X`.
        *   Open the web browser on your mobile device (ensure it's connected to the same Wi-Fi network) and go to `http://<YOUR_COMPUTER_IP>:3000` (replace `<YOUR_COMPUTER_IP>` with the actual IP address you found).

4.  **Stop the server:** Press `Ctrl + C` in the terminal where the server is running. 